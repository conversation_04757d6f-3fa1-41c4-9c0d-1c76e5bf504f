<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JPanelFormInfo">
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <EmptySpace min="0" pref="947" max="32767" attributes="0"/>
          <Group type="103" rootIndex="1" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                  <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
              </Group>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <EmptySpace min="0" pref="695" max="32767" attributes="0"/>
          <Group type="103" rootIndex="1" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                  <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
              </Group>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="45" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="1" attributes="0">
                      <Component id="jLabel3" alignment="1" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel2" alignment="0" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="97" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" alignment="0" attributes="0">
                          <Component id="btnThem" min="-2" pref="107" max="-2" attributes="0"/>
                          <EmptySpace pref="158" max="32767" attributes="0"/>
                          <Component id="btnXoa" min="-2" pref="106" max="-2" attributes="0"/>
                          <EmptySpace min="-2" pref="157" max="-2" attributes="0"/>
                          <Component id="btnSua" min="-2" pref="106" max="-2" attributes="0"/>
                          <EmptySpace min="-2" pref="62" max="-2" attributes="0"/>
                      </Group>
                      <Component id="txtGia" alignment="0" max="32767" attributes="0"/>
                      <Component id="txtTenDV" alignment="0" max="32767" attributes="0"/>
                      <Component id="txtMaDV" alignment="0" max="32767" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="31" max="-2" attributes="0"/>
              </Group>
              <Component id="jScrollPane1" alignment="0" max="32767" attributes="0"/>
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="285" max="-2" attributes="0"/>
                  <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                  <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="41" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="txtMaDV" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="36" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="txtTenDV" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="35" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="txtGia" alignment="0" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace max="32767" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="btnThem" alignment="3" min="-2" pref="46" max="-2" attributes="0"/>
                      <Component id="btnXoa" alignment="3" min="-2" pref="51" max="-2" attributes="0"/>
                      <Component id="btnSua" alignment="3" min="-2" pref="46" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="34" max="-2" attributes="0"/>
                  <Component id="jScrollPane1" min="-2" pref="227" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="125" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="jLabel1">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="40" style="1"/>
            </Property>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="33" red="33" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="QU&#x1ea2;N L&#xdd; D&#x1eca;CH V&#x1ee4;"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel2">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="1"/>
            </Property>
            <Property name="text" type="java.lang.String" value="M&#xe3; D&#x1ecb;ch V&#x1ee5;"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel3">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="1"/>
            </Property>
            <Property name="text" type="java.lang.String" value="T&#xea;n D&#x1ecb;ch V&#x1ee5;"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel4">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="1"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Gi&#xe1;"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="btnThem">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="1"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Th&#xea;m"/>
            <Property name="name" type="java.lang.String" value="btnThem" noResource="true"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnThemActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="btnXoa">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="1"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Xo&#xe1;"/>
            <Property name="name" type="java.lang.String" value="btnXoa" noResource="true"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnXoaActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="btnSua">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="14" style="1"/>
            </Property>
            <Property name="text" type="java.lang.String" value="S&#x1eed;a"/>
            <Property name="name" type="java.lang.String" value="btnSua" noResource="true"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnSuaActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JTextField" name="txtMaDV">
        </Component>
        <Component class="javax.swing.JTextField" name="txtTenDV">
        </Component>
        <Component class="javax.swing.JTextField" name="txtGia">
        </Component>
        <Container class="javax.swing.JScrollPane" name="jScrollPane1">
          <AuxValues>
            <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
          </AuxValues>

          <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
          <SubComponents>
            <Component class="javax.swing.JTable" name="tblDichVu">
              <Properties>
                <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
                  <Table columnCount="3" rowCount="3">
                    <Column editable="true" title="M&#xe3; D&#x1ecb;ch V&#x1ee5;" type="java.lang.Object"/>
                    <Column editable="true" title="T&#xea;n D&#x1ecb;ch V&#x1ee5;" type="java.lang.Object"/>
                    <Column editable="true" title="Gi&#xe1;" type="java.lang.Object"/>
                  </Table>
                </Property>
              </Properties>
            </Component>
          </SubComponents>
        </Container>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
