<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JPanelFormInfo">
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace max="32767" attributes="0"/>
              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="265" max="-2" attributes="0"/>
          </Group>
          <Group type="102" attributes="0">
              <EmptySpace min="-2" pref="68" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel2" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="59" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="1" max="-2" attributes="0">
                  <Group type="102" attributes="0">
                      <Component id="cboTrangThai" min="-2" pref="479" max="-2" attributes="0"/>
                      <EmptySpace pref="85" max="32767" attributes="0"/>
                      <Component id="btnTimKiem" min="-2" pref="121" max="-2" attributes="0"/>
                  </Group>
                  <Component id="jTextField1" alignment="0" max="32767" attributes="0"/>
                  <Component id="jTextField2" alignment="0" max="32767" attributes="0"/>
                  <Component id="jTextField3" alignment="0" max="32767" attributes="0"/>
              </Group>
              <EmptySpace pref="30" max="32767" attributes="0"/>
          </Group>
          <Component id="jScrollPane1" alignment="1" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace pref="13" max="32767" attributes="0"/>
              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="26" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jTextField1" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel3" alignment="3" min="-2" pref="22" max="-2" attributes="0"/>
                  <Component id="jTextField2" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jTextField3" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="cboTrangThai" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Component id="btnTimKiem" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="14" max="-2" attributes="0"/>
              <Component id="jScrollPane1" min="-2" pref="340" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="38" style="1"/>
        </Property>
        <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="0" red="0" type="rgb"/>
        </Property>
        <Property name="text" type="java.lang.String" value="TRA CUU DON HANG"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="M&#xe3; &#x110;&#x1a1;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="T&#xea;n Kh&#xe1;ch H&#xe0;ng"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="S&#x1ed1; &#x110;i&#x1ec7;n Tho&#x1ea1;i"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Tr&#x1ea1;ng Th&#xe1;i"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JComboBox" name="cboTrangThai">
      <Properties>
        <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
          <StringArray count="4">
            <StringItem index="0" value="Item 1"/>
            <StringItem index="1" value="Item 2"/>
            <StringItem index="2" value="Item 3"/>
            <StringItem index="3" value="Item 4"/>
          </StringArray>
        </Property>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_TypeParameters" type="java.lang.String" value="&lt;String&gt;"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JTextField" name="jTextField1">
    </Component>
    <Component class="javax.swing.JTextField" name="jTextField2">
    </Component>
    <Component class="javax.swing.JTextField" name="jTextField3">
    </Component>
    <Component class="javax.swing.JButton" name="btnTimKiem">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="T&#xec;m Ki&#x1ebf;m"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnTimKiemActionPerformed"/>
      </Events>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="jTable1">
          <Properties>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="3" rowCount="3">
                <Column editable="true" title="T&#xea;n Kh&#xe1;ch H&#xe0;ng" type="java.lang.Object"/>
                <Column editable="true" title="M&#xe3; &#x110;&#x1a1;n" type="java.lang.Object"/>
                <Column editable="true" title="S&#x1ed1; &#x110;i&#x1ec7;n Tho&#x1ea1;i" type="java.lang.Object"/>
              </Table>
            </Property>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
