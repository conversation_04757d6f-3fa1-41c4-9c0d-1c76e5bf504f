/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/GUIForms/JPanel.java to edit this template
 */
package view;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ThanhToanDonHangPanel extends javax.swing.JPanel {

    // Danh sách lưu trữ các đơn hàng
    private List<DonHang> danhSachDonHang;
    private DefaultTableModel tableModel;
    private DecimalFormat currencyFormat;

    /**
     * Creates new form ThanhToanDonHangPanel
     */
    public ThanhToanDonHangPanel() {
        initComponents();
        initializeData();
        setupTableEvents();
        setupButtonEvents();
    }

    /**
     * Khởi tạo dữ liệu và cấu hình ban đầu
     */
    private void initializeData() {
        danhSachDonHang = new ArrayList<>();
        currencyFormat = new DecimalFormat("#,###,### VNĐ");
        tableModel = (DefaultTableModel) tblTTDonHang.getModel();

        // Xóa dữ liệu mẫu trong bảng
        tableModel.setRowCount(0);

        // Thiết lập các trường chỉ đọc
        txtTongTien.setEditable(false);

        // Load dữ liệu mẫu
        loadSampleData();
    }

    /**
     * Thiết lập sự kiện cho bảng
     */
    private void setupTableEvents() {
        tblTTDonHang.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 1) {
                    int selectedRow = tblTTDonHang.getSelectedRow();
                    if (selectedRow >= 0) {
                        loadDataToForm(selectedRow);
                    }
                }
            }
        });
    }

    /**
     * Thiết lập sự kiện cho các nút
     */
    private void setupButtonEvents() {
        btnThanhThoan.addActionListener(e -> thanhToanDonHang());
        setupContextMenu();
        setupKeyboardShortcuts();
        setupFieldEvents();
    }

    /**
     * Thiết lập phím tắt
     */
    private void setupKeyboardShortcuts() {
        // Ctrl+N: Thêm đơn hàng mới
        this.getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(
            KeyStroke.getKeyStroke("ctrl N"), "themDonHang");
        this.getActionMap().put("themDonHang", new AbstractAction() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                themDonHang();
            }
        });

        // Ctrl+P: Thanh toán
        this.getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(
            KeyStroke.getKeyStroke("ctrl P"), "thanhToan");
        this.getActionMap().put("thanhToan", new AbstractAction() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                thanhToanDonHang();
            }
        });

        // F5: Làm mới
        this.getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(
            KeyStroke.getKeyStroke("F5"), "lamMoi");
        this.getActionMap().put("lamMoi", new AbstractAction() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                clearForm();
                updateTable();
            }
        });

        // Delete: Xóa đơn hàng
        this.getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(
            KeyStroke.getKeyStroke("DELETE"), "xoaDonHang");
        this.getActionMap().put("xoaDonHang", new AbstractAction() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                xoaDonHang();
            }
        });
    }

    /**
     * Thiết lập sự kiện cho các trường nhập liệu
     */
    private void setupFieldEvents() {
        // Tự động tính tiền khi thay đổi đơn giá hoặc số lượng
        txtDonGia.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyReleased(java.awt.event.KeyEvent evt) {
                if (!txtDonGia.getText().trim().isEmpty() &&
                    !txtSoLuong.getText().trim().isEmpty()) {
                    tinhTongTien();
                }
            }
        });

        txtSoLuong.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyReleased(java.awt.event.KeyEvent evt) {
                if (!txtDonGia.getText().trim().isEmpty() &&
                    !txtSoLuong.getText().trim().isEmpty()) {
                    tinhTongTien();
                }
            }
        });

        // Enter để thêm đơn hàng
        txtSoLuong.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyPressed(java.awt.event.KeyEvent evt) {
                if (evt.getKeyCode() == java.awt.event.KeyEvent.VK_ENTER) {
                    themDonHang();
                }
            }
        });
    }

    /**
     * Thiết lập menu chuột phải cho bảng
     */
    private void setupContextMenu() {
        JPopupMenu contextMenu = new JPopupMenu();

        JMenuItem themDonHangItem = new JMenuItem("Thêm đơn hàng");
        JMenuItem xoaDonHangItem = new JMenuItem("Xóa đơn hàng");
        JMenuItem thongKeItem = new JMenuItem("Xem thống kê");
        JMenuItem xuatBaoCaoItem = new JMenuItem("Xuất báo cáo");
        JMenuItem lamMoiItem = new JMenuItem("Làm mới");

        themDonHangItem.addActionListener(e -> themDonHang());
        xoaDonHangItem.addActionListener(e -> xoaDonHang());
        thongKeItem.addActionListener(e -> hienThiThongKe());
        xuatBaoCaoItem.addActionListener(e -> xuatBaoCao());
        lamMoiItem.addActionListener(e -> {
            clearForm();
            updateTable();
        });

        contextMenu.add(themDonHangItem);
        contextMenu.add(xoaDonHangItem);
        contextMenu.addSeparator();
        contextMenu.add(thongKeItem);
        contextMenu.add(xuatBaoCaoItem);
        contextMenu.addSeparator();
        contextMenu.add(lamMoiItem);

        tblTTDonHang.setComponentPopupMenu(contextMenu);
    }

    /**
     * Xóa đơn hàng được chọn
     */
    private void xoaDonHang() {
        int selectedRow = tblTTDonHang.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn đơn hàng cần xóa!",
                "Thông báo", JOptionPane.WARNING_MESSAGE);
            return;
        }

        DonHang donHang = danhSachDonHang.get(selectedRow);

        int confirm = JOptionPane.showConfirmDialog(this,
            "Bạn có chắc chắn muốn xóa đơn hàng: " + donHang.getDichVu() + "?",
            "Xác nhận xóa", JOptionPane.YES_NO_OPTION);

        if (confirm == JOptionPane.YES_OPTION) {
            danhSachDonHang.remove(selectedRow);
            updateTable();
            clearForm();

            JOptionPane.showMessageDialog(this, "Xóa đơn hàng thành công!",
                "Thành công", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * Load dữ liệu mẫu
     */
    private void loadSampleData() {
        danhSachDonHang.add(new DonHang("DV001", "Giặt áo sơ mi", 25000, 3, "Chưa thanh toán"));
        danhSachDonHang.add(new DonHang("DV002", "Giặt quần âu", 30000, 2, "Chưa thanh toán"));
        danhSachDonHang.add(new DonHang("DV003", "Ủi đồ", 15000, 5, "Chưa thanh toán"));
        danhSachDonHang.add(new DonHang("DV004", "Giặt khô", 50000, 1, "Đã thanh toán"));

        updateTable();
    }

    /**
     * Cập nhật dữ liệu lên bảng
     */
    private void updateTable() {
        tableModel.setRowCount(0);
        for (DonHang dh : danhSachDonHang) {
            tableModel.addRow(new Object[]{
                dh.getDichVu(),
                currencyFormat.format(dh.getDonGia()),
                dh.getSoLuong(),
                currencyFormat.format(dh.getTongTien()),
                dh.getTrangThai()
            });
        }
    }

    /**
     * Load dữ liệu từ bảng lên form
     */
    private void loadDataToForm(int selectedRow) {
        if (selectedRow >= 0 && selectedRow < danhSachDonHang.size()) {
            DonHang dh = danhSachDonHang.get(selectedRow);
            txtDichVu.setText(dh.getDichVu());
            txtDonGia.setText(String.valueOf(dh.getDonGia()));
            txtSoLuong.setText(String.valueOf(dh.getSoLuong()));
            txtTongTien.setText(currencyFormat.format(dh.getTongTien()));
        }
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jLabel1 = new javax.swing.JLabel();
        jLabel2 = new javax.swing.JLabel();
        jLabel3 = new javax.swing.JLabel();
        jLabel4 = new javax.swing.JLabel();
        jLabel5 = new javax.swing.JLabel();
        txtDichVu = new javax.swing.JTextField();
        txtDonGia = new javax.swing.JTextField();
        txtSoLuong = new javax.swing.JTextField();
        txtTongTien = new javax.swing.JTextField();
        btnTinhTien = new javax.swing.JButton();
        btnThanhThoan = new javax.swing.JButton();
        jScrollPane1 = new javax.swing.JScrollPane();
        tblTTDonHang = new javax.swing.JTable();

        jLabel1.setFont(new java.awt.Font("Segoe UI", 1, 36)); // NOI18N
        jLabel1.setForeground(new java.awt.Color(0, 0, 255));
        jLabel1.setText("THANH TOÁN ĐƠN HÀNG");

        jLabel2.setFont(new java.awt.Font("Segoe UI", 1, 14)); // NOI18N
        jLabel2.setText("Dịch Vụ ");

        jLabel3.setFont(new java.awt.Font("Segoe UI", 1, 14)); // NOI18N
        jLabel3.setText("Đơn Giá");

        jLabel4.setFont(new java.awt.Font("Segoe UI", 1, 14)); // NOI18N
        jLabel4.setText("Số Lượng");

        jLabel5.setFont(new java.awt.Font("Segoe UI", 1, 14)); // NOI18N
        jLabel5.setText("Tổng Tiền");

        txtTongTien.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtTongTienActionPerformed(evt);
            }
        });

        btnTinhTien.setFont(new java.awt.Font("Segoe UI", 1, 14)); // NOI18N
        btnTinhTien.setText("Tính Tiền");
        btnTinhTien.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnTinhTienActionPerformed(evt);
            }
        });

        btnThanhThoan.setFont(new java.awt.Font("Segoe UI", 1, 14)); // NOI18N
        btnThanhThoan.setText("Thanh Toán");

        tblTTDonHang.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {
                {null, null, null, null, null},
                {null, null, null, null, null},
                {null, null, null, null, null},
                {null, null, null, null, null},
                {null, null, null, null, null}
            },
            new String [] {
                "Dịch Vụ", "Đơn Giá", "Số Lượng", "Tổng Tiền", "Trạng Thái"
            }
        ));
        jScrollPane1.setViewportView(tblTTDonHang);

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(this);
        this.setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, 923, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(0, 0, Short.MAX_VALUE))
            .addGroup(layout.createSequentialGroup()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addGap(73, 73, 73)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addComponent(jLabel2)
                            .addComponent(jLabel3)
                            .addComponent(jLabel4)
                            .addComponent(jLabel5))
                        .addGap(68, 68, 68)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING, false)
                            .addComponent(txtTongTien, javax.swing.GroupLayout.Alignment.LEADING, javax.swing.GroupLayout.DEFAULT_SIZE, 629, Short.MAX_VALUE)
                            .addComponent(txtSoLuong, javax.swing.GroupLayout.Alignment.LEADING)
                            .addComponent(txtDonGia, javax.swing.GroupLayout.Alignment.LEADING)
                            .addComponent(txtDichVu, javax.swing.GroupLayout.Alignment.LEADING)))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(276, 276, 276)
                        .addComponent(btnTinhTien, javax.swing.GroupLayout.PREFERRED_SIZE, 132, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addGap(123, 123, 123)
                        .addComponent(btnThanhThoan, javax.swing.GroupLayout.PREFERRED_SIZE, 132, javax.swing.GroupLayout.PREFERRED_SIZE))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(250, 250, 250)
                        .addComponent(jLabel1)))
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGap(15, 15, 15)
                .addComponent(jLabel1)
                .addGap(30, 30, 30)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(jLabel2)
                    .addComponent(txtDichVu, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addGap(22, 22, 22)
                        .addComponent(jLabel3))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(18, 18, 18)
                        .addComponent(txtDonGia, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)))
                .addGap(24, 24, 24)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(jLabel4)
                    .addComponent(txtSoLuong, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(23, 23, 23)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel5)
                    .addComponent(txtTongTien, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(62, 62, 62)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(btnThanhThoan, javax.swing.GroupLayout.PREFERRED_SIZE, 56, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(btnTinhTien, javax.swing.GroupLayout.PREFERRED_SIZE, 56, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, 8, Short.MAX_VALUE)
                .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, 223, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(65, 65, 65))
        );
    }// </editor-fold>//GEN-END:initComponents

    private void txtTongTienActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtTongTienActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtTongTienActionPerformed

    private void btnTinhTienActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btnTinhTienActionPerformed
        tinhTongTien();
    }//GEN-LAST:event_btnTinhTienActionPerformed

    /**
     * Tính tổng tiền dựa trên đơn giá và số lượng
     */
    private void tinhTongTien() {
        try {
            String donGiaStr = txtDonGia.getText().trim();
            String soLuongStr = txtSoLuong.getText().trim();

            if (donGiaStr.isEmpty() || soLuongStr.isEmpty()) {
                JOptionPane.showMessageDialog(this, "Vui lòng nhập đơn giá và số lượng!",
                    "Thông báo", JOptionPane.WARNING_MESSAGE);
                return;
            }

            double donGia = Double.parseDouble(donGiaStr);
            int soLuong = Integer.parseInt(soLuongStr);

            if (donGia < 0 || soLuong < 0) {
                JOptionPane.showMessageDialog(this, "Đơn giá và số lượng phải lớn hơn 0!",
                    "Lỗi", JOptionPane.ERROR_MESSAGE);
                return;
            }

            double tongTien = donGia * soLuong;
            txtTongTien.setText(currencyFormat.format(tongTien));

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập số hợp lệ!",
                "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Thêm đơn hàng mới
     */
    private void themDonHang() {
        String dichVu = txtDichVu.getText().trim();
        String donGiaStr = txtDonGia.getText().trim();
        String soLuongStr = txtSoLuong.getText().trim();

        // Kiểm tra tính hợp lệ của dữ liệu
        if (!validateInput(dichVu, donGiaStr, soLuongStr)) {
            return;
        }

        // Kiểm tra trùng lặp
        if (isDonHangExists(dichVu)) {
            int confirm = JOptionPane.showConfirmDialog(this,
                "Dịch vụ '" + dichVu + "' đã tồn tại. Bạn có muốn thêm mới không?",
                "Xác nhận", JOptionPane.YES_NO_OPTION);
            if (confirm != JOptionPane.YES_OPTION) {
                return;
            }
        }

        try {
            double donGia = Double.parseDouble(donGiaStr);
            int soLuong = Integer.parseInt(soLuongStr);

            DonHang donHangMoi = new DonHang(
                "DV" + String.format("%03d", danhSachDonHang.size() + 1),
                dichVu, donGia, soLuong, "Chưa thanh toán"
            );

            danhSachDonHang.add(donHangMoi);
            updateTable();
            clearForm();

            JOptionPane.showMessageDialog(this,
                "Thêm đơn hàng thành công!\nMã đơn: " + donHangMoi.getMaDichVu(),
                "Thành công", JOptionPane.INFORMATION_MESSAGE);

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Lỗi xử lý dữ liệu số!",
                "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Thanh toán đơn hàng được chọn
     */
    private void thanhToanDonHang() {
        int selectedRow = tblTTDonHang.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn đơn hàng cần thanh toán!",
                "Thông báo", JOptionPane.WARNING_MESSAGE);
            return;
        }

        DonHang donHang = danhSachDonHang.get(selectedRow);

        if ("Đã thanh toán".equals(donHang.getTrangThai())) {
            JOptionPane.showMessageDialog(this, "Đơn hàng này đã được thanh toán!",
                "Thông báo", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        int confirm = JOptionPane.showConfirmDialog(this,
            "Xác nhận thanh toán đơn hàng: " + donHang.getDichVu() +
            "\nTổng tiền: " + currencyFormat.format(donHang.getTongTien()),
            "Xác nhận thanh toán", JOptionPane.YES_NO_OPTION);

        if (confirm == JOptionPane.YES_OPTION) {
            donHang.setTrangThai("Đã thanh toán");
            updateTable();
            clearForm();

            JOptionPane.showMessageDialog(this, "Thanh toán thành công!",
                "Thành công", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * Xóa nội dung form
     */
    private void clearForm() {
        txtDichVu.setText("");
        txtDonGia.setText("");
        txtSoLuong.setText("");
        txtTongTien.setText("");
        tblTTDonHang.clearSelection();
    }

    /**
     * Tính tổng doanh thu
     */
    private double tinhTongDoanhThu() {
        double tongDoanhThu = 0;
        for (DonHang dh : danhSachDonHang) {
            if ("Đã thanh toán".equals(dh.getTrangThai())) {
                tongDoanhThu += dh.getTongTien();
            }
        }
        return tongDoanhThu;
    }

    /**
     * Hiển thị thống kê
     */
    private void hienThiThongKe() {
        int tongDonHang = danhSachDonHang.size();
        int donHangDaThanhToan = 0;
        int donHangChuaThanhToan = 0;

        for (DonHang dh : danhSachDonHang) {
            if ("Đã thanh toán".equals(dh.getTrangThai())) {
                donHangDaThanhToan++;
            } else {
                donHangChuaThanhToan++;
            }
        }

        double tongDoanhThu = tinhTongDoanhThu();

        String thongKe = String.format(
            "=== THỐNG KÊ ===\n" +
            "Tổng số đơn hàng: %d\n" +
            "Đã thanh toán: %d\n" +
            "Chưa thanh toán: %d\n" +
            "Tổng doanh thu: %s",
            tongDonHang, donHangDaThanhToan, donHangChuaThanhToan,
            currencyFormat.format(tongDoanhThu)
        );

        JOptionPane.showMessageDialog(this, thongKe, "Thống kê", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * Tìm kiếm đơn hàng theo tên dịch vụ
     */
    private void timKiemDonHang(String keyword) {
        if (keyword.trim().isEmpty()) {
            updateTable();
            return;
        }

        tableModel.setRowCount(0);
        for (DonHang dh : danhSachDonHang) {
            if (dh.getDichVu().toLowerCase().contains(keyword.toLowerCase())) {
                tableModel.addRow(new Object[]{
                    dh.getDichVu(),
                    currencyFormat.format(dh.getDonGia()),
                    dh.getSoLuong(),
                    currencyFormat.format(dh.getTongTien()),
                    dh.getTrangThai()
                });
            }
        }
    }

    /**
     * Xuất báo cáo đơn hàng
     */
    private void xuatBaoCao() {
        StringBuilder baoCao = new StringBuilder();
        baoCao.append("=== BÁO CÁO ĐƠN HÀNG ===\n\n");

        for (int i = 0; i < danhSachDonHang.size(); i++) {
            DonHang dh = danhSachDonHang.get(i);
            baoCao.append(String.format("%d. %s\n", i + 1, dh.toString()));
        }

        baoCao.append(String.format("\nTổng doanh thu: %s",
            currencyFormat.format(tinhTongDoanhThu())));

        JTextArea textArea = new JTextArea(baoCao.toString());
        textArea.setEditable(false);
        textArea.setFont(new java.awt.Font("Monospaced", java.awt.Font.PLAIN, 12));

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new java.awt.Dimension(500, 400));

        JOptionPane.showMessageDialog(this, scrollPane, "Báo cáo đơn hàng",
            JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * Kiểm tra tính hợp lệ của dữ liệu đầu vào
     */
    private boolean validateInput(String dichVu, String donGiaStr, String soLuongStr) {
        if (dichVu.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Tên dịch vụ không được để trống!",
                "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtDichVu.requestFocus();
            return false;
        }

        if (donGiaStr.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Đơn giá không được để trống!",
                "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtDonGia.requestFocus();
            return false;
        }

        if (soLuongStr.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Số lượng không được để trống!",
                "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtSoLuong.requestFocus();
            return false;
        }

        try {
            double donGia = Double.parseDouble(donGiaStr);
            if (donGia <= 0) {
                JOptionPane.showMessageDialog(this, "Đơn giá phải lớn hơn 0!",
                    "Lỗi", JOptionPane.ERROR_MESSAGE);
                txtDonGia.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Đơn giá phải là số hợp lệ!",
                "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtDonGia.requestFocus();
            return false;
        }

        try {
            int soLuong = Integer.parseInt(soLuongStr);
            if (soLuong <= 0) {
                JOptionPane.showMessageDialog(this, "Số lượng phải lớn hơn 0!",
                    "Lỗi", JOptionPane.ERROR_MESSAGE);
                txtSoLuong.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Số lượng phải là số nguyên hợp lệ!",
                "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtSoLuong.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * Định dạng số tiền
     */
    private String formatCurrency(double amount) {
        return currencyFormat.format(amount);
    }

    /**
     * Lấy số từ chuỗi đã định dạng tiền tệ
     */
    private double parseCurrency(String formattedAmount) {
        try {
            return currencyFormat.parse(formattedAmount).doubleValue();
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * Kiểm tra xem đơn hàng có tồn tại không
     */
    private boolean isDonHangExists(String dichVu) {
        for (DonHang dh : danhSachDonHang) {
            if (dh.getDichVu().equalsIgnoreCase(dichVu.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Cập nhật trạng thái thanh toán hàng loạt
     */
    private void capNhatTrangThaiHangLoat(String trangThaiMoi) {
        int count = 0;
        for (DonHang dh : danhSachDonHang) {
            if (!"Đã thanh toán".equals(dh.getTrangThai()) && "Đã thanh toán".equals(trangThaiMoi)) {
                dh.setTrangThai(trangThaiMoi);
                count++;
            }
        }

        if (count > 0) {
            updateTable();
            JOptionPane.showMessageDialog(this,
                String.format("Đã cập nhật trạng thái cho %d đơn hàng!", count),
                "Thành công", JOptionPane.INFORMATION_MESSAGE);
        } else {
            JOptionPane.showMessageDialog(this, "Không có đơn hàng nào cần cập nhật!",
                "Thông báo", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * Class đại diện cho một đơn hàng
     */
    public static class DonHang {
        private String maDichVu;
        private String dichVu;
        private double donGia;
        private int soLuong;
        private String trangThai;

        public DonHang(String maDichVu, String dichVu, double donGia, int soLuong, String trangThai) {
            this.maDichVu = maDichVu;
            this.dichVu = dichVu;
            this.donGia = donGia;
            this.soLuong = soLuong;
            this.trangThai = trangThai;
        }

        // Getters and Setters
        public String getMaDichVu() { return maDichVu; }
        public void setMaDichVu(String maDichVu) { this.maDichVu = maDichVu; }

        public String getDichVu() { return dichVu; }
        public void setDichVu(String dichVu) { this.dichVu = dichVu; }

        public double getDonGia() { return donGia; }
        public void setDonGia(double donGia) { this.donGia = donGia; }

        public int getSoLuong() { return soLuong; }
        public void setSoLuong(int soLuong) { this.soLuong = soLuong; }

        public String getTrangThai() { return trangThai; }
        public void setTrangThai(String trangThai) { this.trangThai = trangThai; }

        public double getTongTien() {
            return donGia * soLuong;
        }

        @Override
        public String toString() {
            return String.format("Dịch vụ: %s | Đơn giá: %.0f | Số lượng: %d | Tổng tiền: %.0f | Trạng thái: %s",
                dichVu, donGia, soLuong, getTongTien(), trangThai);
        }
    }

    /**
     * Phương thức công khai để thêm đơn hàng từ bên ngoài
     */
    public void addDonHang(String dichVu, double donGia, int soLuong) {
        DonHang donHangMoi = new DonHang(
            "DV" + String.format("%03d", danhSachDonHang.size() + 1),
            dichVu, donGia, soLuong, "Chưa thanh toán"
        );
        danhSachDonHang.add(donHangMoi);
        updateTable();
    }

    /**
     * Lấy danh sách đơn hàng
     */
    public List<DonHang> getDanhSachDonHang() {
        return new ArrayList<>(danhSachDonHang);
    }

    /**
     * Lấy tổng doanh thu
     */
    public double getTongDoanhThu() {
        return tinhTongDoanhThu();
    }


    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton btnThanhThoan;
    private javax.swing.JButton btnTinhTien;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel4;
    private javax.swing.JLabel jLabel5;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTable tblTTDonHang;
    private javax.swing.JTextField txtDichVu;
    private javax.swing.JTextField txtDonGia;
    private javax.swing.JTextField txtSoLuong;
    private javax.swing.JTextField txtTongTien;
    // End of variables declaration//GEN-END:variables
}
