<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JPanelFormInfo">
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="jScrollPane1" min="-2" pref="923" max="-2" attributes="0"/>
              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
          </Group>
          <Group type="102" attributes="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <EmptySpace min="-2" pref="73" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="jLabel2" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace min="-2" pref="68" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="1" max="-2" attributes="0">
                          <Component id="txtTongTien" alignment="0" pref="629" max="32767" attributes="0"/>
                          <Component id="txtSoLuong" alignment="0" max="32767" attributes="0"/>
                          <Component id="txtDonGia" alignment="0" max="32767" attributes="0"/>
                          <Component id="txtDichVu" alignment="0" max="32767" attributes="0"/>
                      </Group>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="276" max="-2" attributes="0"/>
                      <Component id="btnTinhTien" min="-2" pref="132" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="123" max="-2" attributes="0"/>
                      <Component id="btnThanhThoan" min="-2" pref="132" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="250" max="-2" attributes="0"/>
                      <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="15" max="-2" attributes="0"/>
              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
                  <Component id="txtDichVu" min="-2" max="-2" attributes="0"/>
              </Group>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <EmptySpace min="-2" pref="22" max="-2" attributes="0"/>
                      <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace type="separate" max="-2" attributes="0"/>
                      <Component id="txtDonGia" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace min="-2" pref="24" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jLabel4" min="-2" max="-2" attributes="0"/>
                  <Component id="txtSoLuong" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="23" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txtTongTien" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="62" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="btnThanhThoan" alignment="3" min="-2" pref="56" max="-2" attributes="0"/>
                  <Component id="btnTinhTien" alignment="3" min="-2" pref="56" max="-2" attributes="0"/>
              </Group>
              <EmptySpace pref="8" max="32767" attributes="0"/>
              <Component id="jScrollPane1" min="-2" pref="223" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="65" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="36" style="1"/>
        </Property>
        <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="0" red="0" type="rgb"/>
        </Property>
        <Property name="text" type="java.lang.String" value="THANH TO&#xc1;N &#x110;&#x1a0;N H&#xc0;NG"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="D&#x1ecb;ch V&#x1ee5; "/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="&#x110;&#x1a1;n Gi&#xe1;"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="S&#x1ed1; L&#x1b0;&#x1ee3;ng"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="T&#x1ed5;ng Ti&#x1ec1;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txtDichVu">
    </Component>
    <Component class="javax.swing.JTextField" name="txtDonGia">
    </Component>
    <Component class="javax.swing.JTextField" name="txtSoLuong">
    </Component>
    <Component class="javax.swing.JTextField" name="txtTongTien">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txtTongTienActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnTinhTien">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="T&#xed;nh Ti&#x1ec1;n"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnTinhTienActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnThanhThoan">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Thanh To&#xe1;n"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="tblTTDonHang">
          <Properties>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="5" rowCount="5">
                <Column editable="true" title="D&#x1ecb;ch V&#x1ee5;" type="java.lang.Object"/>
                <Column editable="true" title="&#x110;&#x1a1;n Gi&#xe1;" type="java.lang.Object"/>
                <Column editable="true" title="S&#x1ed1; L&#x1b0;&#x1ee3;ng" type="java.lang.Object"/>
                <Column editable="true" title="T&#x1ed5;ng Ti&#x1ec1;n" type="java.lang.Object"/>
                <Column editable="true" title="Tr&#x1ea1;ng Th&#xe1;i" type="java.lang.Object"/>
              </Table>
            </Property>
            <Property name="columnModel" type="javax.swing.table.TableColumnModel" editor="org.netbeans.modules.form.editors2.TableColumnModelEditor">
              <TableColumnModel selectionModel="0">
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
              </TableColumnModel>
            </Property>
            <Property name="tableHeader" type="javax.swing.table.JTableHeader" editor="org.netbeans.modules.form.editors2.JTableHeaderEditor">
              <TableHeader reorderingAllowed="true" resizingAllowed="true"/>
            </Property>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
