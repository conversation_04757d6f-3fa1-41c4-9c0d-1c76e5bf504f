<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JPanelFormInfo">
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="219" max="-2" attributes="0"/>
                      <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="54" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="1" attributes="0">
                          <Group type="103" alignment="1" groupAlignment="0" attributes="0">
                              <Component id="jLabel2" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel6" alignment="0" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="1" attributes="0">
                              <Component id="jLabel7" min="-2" max="-2" attributes="0"/>
                              <EmptySpace min="-2" pref="17" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                      <EmptySpace min="-2" pref="35" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="jTextField1" alignment="0" max="32767" attributes="0"/>
                          <Component id="jTextField2" alignment="0" max="32767" attributes="0"/>
                          <Component id="jTextField3" alignment="0" max="32767" attributes="0"/>
                          <Component id="jTextField5" alignment="0" max="32767" attributes="0"/>
                          <Component id="jTextField4" alignment="0" max="32767" attributes="0"/>
                          <Group type="102" alignment="0" attributes="0">
                              <EmptySpace min="-2" pref="8" max="-2" attributes="0"/>
                              <Component id="btnTinhTien" min="-2" pref="116" max="-2" attributes="0"/>
                              <EmptySpace min="-2" pref="114" max="-2" attributes="0"/>
                              <Component id="btnThanhToan" min="-2" pref="116" max="-2" attributes="0"/>
                              <EmptySpace max="32767" attributes="0"/>
                              <Component id="btnInHoaDon" min="-2" pref="116" max="-2" attributes="0"/>
                              <EmptySpace min="-2" pref="57" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                  </Group>
              </Group>
              <EmptySpace min="-2" pref="73" max="-2" attributes="0"/>
          </Group>
          <Group type="102" alignment="1" attributes="0">
              <EmptySpace min="0" pref="12" max="32767" attributes="0"/>
              <Component id="jScrollPane1" min="-2" pref="884" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="17" max="-2" attributes="0"/>
              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jTextField1" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jTextField2" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="26" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jTextField3" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel6" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jTextField4" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="28" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel7" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jTextField5" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="42" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="btnTinhTien" alignment="3" min="-2" pref="51" max="-2" attributes="0"/>
                  <Component id="btnThanhToan" alignment="3" min="-2" pref="51" max="-2" attributes="0"/>
                  <Component id="btnInHoaDon" alignment="3" min="-2" pref="51" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="37" max="-2" attributes="0"/>
              <Component id="jScrollPane1" min="-2" pref="255" max="-2" attributes="0"/>
              <EmptySpace pref="12" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="40" style="1"/>
        </Property>
        <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="0" red="0" type="rgb"/>
        </Property>
        <Property name="text" type="java.lang.String" value="THANH TO&#xc1;N HO&#xc1; &#x110;&#x1a0;N"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Kh&#xe1;ch H&#xe0;ng"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="M&#xe3; &#x110;&#x1a1;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="&#x110;&#x1a1;n Gi&#xe1;"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel6">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="S&#x1ed1; L&#x1b0;&#x1ee3;ng"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="jTextField1">
    </Component>
    <Component class="javax.swing.JTextField" name="jTextField2">
    </Component>
    <Component class="javax.swing.JTextField" name="jTextField3">
    </Component>
    <Component class="javax.swing.JTextField" name="jTextField4">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel7">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="T&#x1ed5;ng Ti&#x1ec1;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="jTextField5">
    </Component>
    <Component class="javax.swing.JButton" name="btnTinhTien">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="T&#xed;nh TI&#x1ec1;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="btnThanhToan">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Thanh To&#xe1;n "/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="btnInHoaDon">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="In Ho&#xe1; &#x110;&#x1a1;n"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="jTable1">
          <Properties>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="5" rowCount="5">
                <Column editable="true" title="Kh&#xe1;ch H&#xe0;ng " type="java.lang.Object"/>
                <Column editable="true" title="M&#xe3; &#x110;&#x1a1;n" type="java.lang.Object"/>
                <Column editable="true" title="&#x110;&#x1a1;n Gi&#xe1;" type="java.lang.Object"/>
                <Column editable="true" title="S&#x1ed1; L&#x1b0;&#x1ee3;ng " type="java.lang.Object"/>
                <Column editable="true" title="T&#x1ed5;ng Ti&#x1ec1;n" type="java.lang.Object"/>
              </Table>
            </Property>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
